services:
  texan.gateway.api:
    image: ${DOCKER_REGISTRY-}texangatewayapi
    build:
      context: src
      dockerfile: gateway/Texan.Gateway.Api/Dockerfile
    depends_on:
      - texan.logging.api

  texan.logging.loki:
    image: grafana/loki:2.9.4

  texan.logging.grafana:
    image: grafana/grafana:latest

  texan.logging.api:
    image: ${DOCKER_REGISTRY-}texanloggingbrokerapi
    build:
      context: .
      dockerfile: src/logging/logging-broker/DockerFile
    depends_on:
      - texan.logging.loki

  texan.auth.keycloak.postgres:
    image: postgres:15
    restart: unless-stopped

  texan.auth.keycloak:
    image: quay.io/keycloak/keycloak:26.2.4
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "cat < /dev/tcp/localhost/8080 > /dev/null"]
      interval: 10s
      timeout: 5s
      retries: 12
    depends_on:
      - texan.auth.keycloak.postgres

  texan.auth.api:
    image: ${DOCKER_REGISTRY-}texanauthbrokerpresentationapi
    build:
      context: src
      dockerfile: auth/Texan.AuthBroker.Presentation.WebApi/Dockerfile
    depends_on:
      - texan.auth.keycloak
      - texan.logging.api

  texan.classmanagement.api:
    image: ${DOCKER_REGISTRY-}texanclassmanagementpresentationapi
    build:
      context: src
      dockerfile: class-management/Texan.ClassManagement.Presentation.WebApi/Dockerfile
    depends_on:
      - texan.logging.api
