﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36127.28
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "auth", "auth", "{E4E97138-3BB8-4492-A174-7B5EC1562472}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.AuthBroker.Domain", "src\auth\Texan.AuthBroker.Domain\Texan.AuthBroker.Domain.csproj", "{0BB3C1EB-E8D2-98FA-B782-87ADA34D0852}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.AuthBroker.Application", "src\auth\Texan.AuthBroker.Application\Texan.AuthBroker.Application.csproj", "{591E5D50-74BA-ABC6-A22B-07932670150A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.AuthBroker.Infrastructure", "src\auth\Texan.AuthBroker.Infrastructure\Texan.AuthBroker.Infrastructure.csproj", "{CD0CA9C1-669D-3B66-FF51-D255A156EDE6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.AuthBroker.Presentation.Api", "src\auth\Texan.AuthBroker.Presentation.WebApi\Texan.AuthBroker.Presentation.Api.csproj", "{2D740123-CEEC-1376-DCFF-D196B1BE2D3A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging", "logging", "{4DCB7103-E4C0-42CE-880A-488DA74E90E1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{FD3C35E6-61B3-4D5F-AA85-33CA0B5A5409}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "class-management", "class-management", "{CC8F0981-F097-469F-A284-368F6B87811B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Domain", "src\class-management\Texan.ClassManagement.Domain\Texan.ClassManagement.Domain.csproj", "{0188398A-7E55-AD6F-7E6D-C08ED2DD9295}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Application", "src\class-management\Texan.ClassManagement.Application\Texan.ClassManagement.Application.csproj", "{A53C2009-AD1D-0C5A-2C34-CB39661EAC6D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Infrastructure", "src\class-management\Texan.ClassManagement.Infrastructure\Texan.ClassManagement.Infrastructure.csproj", "{B03B5E5D-DB1E-D028-2429-1335E64DB299}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Presentation.Api", "src\class-management\Texan.ClassManagement.Presentation.WebApi\Texan.ClassManagement.Presentation.Api.csproj", "{5406D325-D614-EC8F-50B8-FBD3AB43FB12}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "gateway", "gateway", "{94D77FC4-0120-4354-B83F-E78A0DE67043}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.Gateway.Api", "src\gateway\Texan.Gateway.Api\Texan.Gateway.Api.csproj", "{D315C4A5-1C3C-315A-A5B9-DE9C0194AF71}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{465450A1-9948-4015-AA12-B98A874B949A}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "src\docker-compose.dcproj", "{81DDED9D-158B-E303-5F62-77A2896D2A5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.Logging.Tests.Integration", "tests\logging\Texan.Logging.Tests.Integration\Texan.Logging.Tests.Integration.csproj", "{89226FBC-3070-3FB2-1394-A9B04C2CAA5D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.Logging.Tests.UnitTests", "tests\logging\Texan.Logging.Tests.UnitTests\Texan.Logging.Tests.UnitTests.csproj", "{E597671A-61AC-B822-840D-E8B8299D638C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging", "logging", "{A3A969D8-0CB4-40FE-90EF-4346F727391C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "storage", "storage", "{94DC48E1-F618-495E-9255-F1F610A1F121}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "student", "student", "{8E1049EA-4DD6-4AFF-B326-1344FF3E6060}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "notification", "notification", "{DF27B4CD-2620-4788-8000-03D2A479E36B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "grading", "grading", "{B298B985-F6C1-41B4-844A-F627E067230B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "postman", "postman", "{6E2954FB-27FF-4083-A6BA-A92F7621FCDD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "common", "common", "{501F6176-D145-45C0-AAD5-************}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.Common", "src\common\Texan.Common\Texan.Common.csproj", "{40E82528-C4D8-4626-9072-6582E6F94C26}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LoggingBroker.Client", "src\logging\LoggingBroker.Client\LoggingBroker.Client.csproj", "{B1A2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestClientLibraryWebApi", "src\logging\TestClientLibraryWebApi\TestClientLibraryWebApi.csproj", "{0F9FCE74-DD98-4321-97D8-5762BFB8C36E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.LoggingBroker.Domain", "src\logging\logging-broker\Texan.LoggingBroker.Domain\Texan.LoggingBroker.Domain.csproj", "{B2E89E1F-18F3-4BCA-9942-C8FDCFA7595C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.LoggingBroker.Application", "src\logging\logging-broker\Texan.LoggingBroker.Application\Texan.LoggingBroker.Application.csproj", "{A9F79016-9533-4099-B5E1-86123D5F0F2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.LoggingBroker.Infrastructure", "src\logging\logging-broker\Texan.LoggingBroker.Infrastructure\Texan.LoggingBroker.Infrastructure.csproj", "{4B038996-8DE5-40FF-A8BA-46EE5F5E90AD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.LoggingBroker.WebApi", "src\logging\logging-broker\Texan.LoggingBroker.WebApi\Texan.LoggingBroker.WebApi.csproj", "{FDEEF416-AD99-4522-8E9A-37B81C8A47BD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0BB3C1EB-E8D2-98FA-B782-87ADA34D0852}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0BB3C1EB-E8D2-98FA-B782-87ADA34D0852}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0BB3C1EB-E8D2-98FA-B782-87ADA34D0852}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0BB3C1EB-E8D2-98FA-B782-87ADA34D0852}.Release|Any CPU.Build.0 = Release|Any CPU
		{591E5D50-74BA-ABC6-A22B-07932670150A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{591E5D50-74BA-ABC6-A22B-07932670150A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{591E5D50-74BA-ABC6-A22B-07932670150A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{591E5D50-74BA-ABC6-A22B-07932670150A}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD0CA9C1-669D-3B66-FF51-D255A156EDE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD0CA9C1-669D-3B66-FF51-D255A156EDE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD0CA9C1-669D-3B66-FF51-D255A156EDE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD0CA9C1-669D-3B66-FF51-D255A156EDE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D740123-CEEC-1376-DCFF-D196B1BE2D3A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D740123-CEEC-1376-DCFF-D196B1BE2D3A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D740123-CEEC-1376-DCFF-D196B1BE2D3A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D740123-CEEC-1376-DCFF-D196B1BE2D3A}.Release|Any CPU.Build.0 = Release|Any CPU
		{0188398A-7E55-AD6F-7E6D-C08ED2DD9295}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0188398A-7E55-AD6F-7E6D-C08ED2DD9295}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0188398A-7E55-AD6F-7E6D-C08ED2DD9295}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0188398A-7E55-AD6F-7E6D-C08ED2DD9295}.Release|Any CPU.Build.0 = Release|Any CPU
		{A53C2009-AD1D-0C5A-2C34-CB39661EAC6D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A53C2009-AD1D-0C5A-2C34-CB39661EAC6D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A53C2009-AD1D-0C5A-2C34-CB39661EAC6D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A53C2009-AD1D-0C5A-2C34-CB39661EAC6D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B03B5E5D-DB1E-D028-2429-1335E64DB299}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B03B5E5D-DB1E-D028-2429-1335E64DB299}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B03B5E5D-DB1E-D028-2429-1335E64DB299}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B03B5E5D-DB1E-D028-2429-1335E64DB299}.Release|Any CPU.Build.0 = Release|Any CPU
		{5406D325-D614-EC8F-50B8-FBD3AB43FB12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5406D325-D614-EC8F-50B8-FBD3AB43FB12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5406D325-D614-EC8F-50B8-FBD3AB43FB12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5406D325-D614-EC8F-50B8-FBD3AB43FB12}.Release|Any CPU.Build.0 = Release|Any CPU
		{D315C4A5-1C3C-315A-A5B9-DE9C0194AF71}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D315C4A5-1C3C-315A-A5B9-DE9C0194AF71}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D315C4A5-1C3C-315A-A5B9-DE9C0194AF71}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D315C4A5-1C3C-315A-A5B9-DE9C0194AF71}.Release|Any CPU.Build.0 = Release|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{89226FBC-3070-3FB2-1394-A9B04C2CAA5D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{89226FBC-3070-3FB2-1394-A9B04C2CAA5D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{89226FBC-3070-3FB2-1394-A9B04C2CAA5D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{89226FBC-3070-3FB2-1394-A9B04C2CAA5D}.Release|Any CPU.Build.0 = Release|Any CPU
		{E597671A-61AC-B822-840D-E8B8299D638C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E597671A-61AC-B822-840D-E8B8299D638C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E597671A-61AC-B822-840D-E8B8299D638C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E597671A-61AC-B822-840D-E8B8299D638C}.Release|Any CPU.Build.0 = Release|Any CPU
		{40E82528-C4D8-4626-9072-6582E6F94C26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{40E82528-C4D8-4626-9072-6582E6F94C26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{40E82528-C4D8-4626-9072-6582E6F94C26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{40E82528-C4D8-4626-9072-6582E6F94C26}.Release|Any CPU.Build.0 = Release|Any CPU
		{B1A2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B1A2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B1A2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B1A2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{0F9FCE74-DD98-4321-97D8-5762BFB8C36E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F9FCE74-DD98-4321-97D8-5762BFB8C36E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F9FCE74-DD98-4321-97D8-5762BFB8C36E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0F9FCE74-DD98-4321-97D8-5762BFB8C36E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2E89E1F-18F3-4BCA-9942-C8FDCFA7595C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2E89E1F-18F3-4BCA-9942-C8FDCFA7595C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2E89E1F-18F3-4BCA-9942-C8FDCFA7595C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2E89E1F-18F3-4BCA-9942-C8FDCFA7595C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9F79016-9533-4099-B5E1-86123D5F0F2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9F79016-9533-4099-B5E1-86123D5F0F2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9F79016-9533-4099-B5E1-86123D5F0F2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9F79016-9533-4099-B5E1-86123D5F0F2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B038996-8DE5-40FF-A8BA-46EE5F5E90AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B038996-8DE5-40FF-A8BA-46EE5F5E90AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B038996-8DE5-40FF-A8BA-46EE5F5E90AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B038996-8DE5-40FF-A8BA-46EE5F5E90AD}.Release|Any CPU.Build.0 = Release|Any CPU
		{FDEEF416-AD99-4522-8E9A-37B81C8A47BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FDEEF416-AD99-4522-8E9A-37B81C8A47BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FDEEF416-AD99-4522-8E9A-37B81C8A47BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FDEEF416-AD99-4522-8E9A-37B81C8A47BD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E4E97138-3BB8-4492-A174-7B5EC1562472} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{0BB3C1EB-E8D2-98FA-B782-87ADA34D0852} = {E4E97138-3BB8-4492-A174-7B5EC1562472}
		{591E5D50-74BA-ABC6-A22B-07932670150A} = {E4E97138-3BB8-4492-A174-7B5EC1562472}
		{CD0CA9C1-669D-3B66-FF51-D255A156EDE6} = {E4E97138-3BB8-4492-A174-7B5EC1562472}
		{2D740123-CEEC-1376-DCFF-D196B1BE2D3A} = {E4E97138-3BB8-4492-A174-7B5EC1562472}
		{4DCB7103-E4C0-42CE-880A-488DA74E90E1} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{CC8F0981-F097-469F-A284-368F6B87811B} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{0188398A-7E55-AD6F-7E6D-C08ED2DD9295} = {CC8F0981-F097-469F-A284-368F6B87811B}
		{A53C2009-AD1D-0C5A-2C34-CB39661EAC6D} = {CC8F0981-F097-469F-A284-368F6B87811B}
		{B03B5E5D-DB1E-D028-2429-1335E64DB299} = {CC8F0981-F097-469F-A284-368F6B87811B}
		{5406D325-D614-EC8F-50B8-FBD3AB43FB12} = {CC8F0981-F097-469F-A284-368F6B87811B}
		{94D77FC4-0120-4354-B83F-E78A0DE67043} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{D315C4A5-1C3C-315A-A5B9-DE9C0194AF71} = {94D77FC4-0120-4354-B83F-E78A0DE67043}
		{81DDED9D-158B-E303-5F62-77A2896D2A5A} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{89226FBC-3070-3FB2-1394-A9B04C2CAA5D} = {A3A969D8-0CB4-40FE-90EF-4346F727391C}
		{E597671A-61AC-B822-840D-E8B8299D638C} = {A3A969D8-0CB4-40FE-90EF-4346F727391C}
		{A3A969D8-0CB4-40FE-90EF-4346F727391C} = {465450A1-9948-4015-AA12-B98A874B949A}
		{94DC48E1-F618-495E-9255-F1F610A1F121} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{8E1049EA-4DD6-4AFF-B326-1344FF3E6060} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{DF27B4CD-2620-4788-8000-03D2A479E36B} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{B298B985-F6C1-41B4-844A-F627E067230B} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{6E2954FB-27FF-4083-A6BA-A92F7621FCDD} = {FD3C35E6-61B3-4D5F-AA85-33CA0B5A5409}
		{501F6176-D145-45C0-AAD5-************} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{40E82528-C4D8-4626-9072-6582E6F94C26} = {501F6176-D145-45C0-AAD5-************}
		{B1A2C3D4-E5F6-7890-ABCD-EF1234567890} = {4DCB7103-E4C0-42CE-880A-488DA74E90E1}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {26A3DA14-4469-4BC3-9AD3-88B01AA0B2A2}
	EndGlobalSection
EndGlobal
